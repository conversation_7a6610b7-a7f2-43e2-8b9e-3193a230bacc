.panel {
    position: absolute;
    top: 50px;
    left: 1px;
    display: flex;
    flex-direction: column;
    width: 260px;
    border: 1px solid #bcaaa4;
    border-radius: 3px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    opacity: 0.7;
    z-index: 1;
    .gemini-icon {
        height: 20px;
        width: 20px;
        path {
            fill: #409eff;
        } 
    } 
    &:hover {
        opacity: 1;
    }
    .header {
        height: 40px;
        line-height: 40px;
        background: #f5f7fa;
        border-bottom: 1px solid #bcaaa4;
        cursor: move;
        .title {
            font-size: 16px;
            display: inline-block;
            width: 100%;
            text-align: center;
        }
        .close {
            position: absolute;
            right: 5px;
            font-size: 20px;
            padding: 0px;
            height: 40px;
            display: inline-flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }
    }
    .body {
        flex-basis: 200px;
        display: block;
        position: relative;
        min-height: 100px;
        .operations {
            width: 100%;
            background: white;
            text-align: right;
            padding: 2px;
            .operation-icon {
                border: none;
                padding: 5px;
                height: 24px;
                margin: 0;
                display: inline-flex;
                border-radius: 4px;
                background: none;
                .gemini-icon {
                    height: 14px;
                    width: 14px;
                    cursor: pointer;
                    path {
                        fill: #333;
                    }
                }
            }
            .operation-icon:hover {
                background: #ecf5ff;
                .gemini-icon {
                    path {
                        fill: #409eff;
                    }
                }
            }
        }
        .viewpoints {
            overflow-y: auto;
            overflow-x: hidden;
            height: 200px; // TODO: the height should be decided by panel height
            .viewpoint {
                padding: 2px;
                margin: 2px;
                cursor: pointer;
                position: relative;
                .snapshot {
                    height: 50px;
                    width: 100%;
                    opacity: 0.7;
                    object-fit: cover;
                    &:hover {
                        opacity: 1;
                        border: solid 1px steelblue;
                    }
                }
                .delete {
                    position: absolute;
                    right: 10px;
                    bottom: 15px;
                    opacity: 0.3;
                    &:hover {
                        opacity: 1;
                    }
                    .gemini-icon {
                        height: 16px;
                        width: 16px;
                        path {
                            fill: #333;
                        }
                    }
                }
            }
        }
    }
    .footer {
        height: 18px;
        .resize {
            position: absolute;
            right: 2px;
            bottom: 2px;
            padding: 0px;
            line-height: 16px;
            cursor: nwse-resize;
            color: #409eff;
            i {
                transform: rotate(315deg);
            }
        }
    }
}